package controllers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/common/response"
	"shikeyinxiang/internal/consts"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/service"
)

// AdminFoodController 管理端食物控制器，处理管理员的食物和分类管理HTTP请求
type AdminFoodController struct {
	foodSvc     service.IFoodService
	categorySvc service.IFoodCategoryService
}

// NewAdminFoodController 创建一个新的 AdminFoodController 实例
func NewAdminFoodController(foodSvc service.IFoodService, categorySvc service.IFoodCategoryService) *AdminFoodController {
	return &AdminFoodController{
		foodSvc:     foodSvc,
		categorySvc: categorySvc,
	}
}

// bindJSONAndValidate 统一处理JSON绑定和验证
func (afc *AdminFoodController) bindJSONAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// bindQueryAndValidate 统一处理Query参数绑定和验证
func (afc *AdminFoodController) bindQueryAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindQuery(req); err != nil {
		response.Error(c, consts.CodeParameterError)
		return false
	}
	return true
}

// parseIDParam 解析路径参数中的ID
func (afc *AdminFoodController) parseIDParam(c *gin.Context, paramName string) (int, bool) {
	idStr := c.Param(paramName)
	if idStr == "" {
		response.Error(c, consts.CodeParameterError)
		return 0, false
	}

	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		response.Error(c, consts.CodeParameterError)
		return 0, false
	}

	return id, true
}

// getCurrentUserID 获取当前用户ID
func (afc *AdminFoodController) getCurrentUserID(c *gin.Context) (int64, bool) {
	userID, exists := middleware.GetCurrentUserID(c)
	if !exists {
		response.Error(c, consts.CodeUnauthorized)
		return 0, false
	}
	return userID, true
}

// ===== 食物管理方法 =====

// CreateFood 创建食物
func (afc *AdminFoodController) CreateFood(c *gin.Context) {
	var req v1.FoodCreateReq
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := afc.foodSvc.CreateFood(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateFood 更新食物
func (afc *AdminFoodController) UpdateFood(c *gin.Context) {
	var req v1.FoodUpdateReq
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := afc.foodSvc.UpdateFood(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// DeleteFood 删除食物
func (afc *AdminFoodController) DeleteFood(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	err := afc.foodSvc.DeleteFood(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "删除成功"})
}

// GetFoodList 获取食物列表（管理端）
func (afc *AdminFoodController) GetFoodList(c *gin.Context) {
	var req v1.FoodQueryReq
	if !afc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	res, err := afc.foodSvc.ListFoods(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// GetFoodDetail 获取食物详情（管理端）
func (afc *AdminFoodController) GetFoodDetail(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	res, err := afc.foodSvc.GetFood(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// ===== 分类管理方法 =====

// CreateCategory 创建食物分类
func (afc *AdminFoodController) CreateCategory(c *gin.Context) {
	var req v1.FoodCategoryCreateReq
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := afc.categorySvc.CreateCategory(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateCategory 更新食物分类
func (afc *AdminFoodController) UpdateCategory(c *gin.Context) {
	var req v1.FoodCategoryUpdateReq
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	res, err := afc.categorySvc.UpdateCategory(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// DeleteCategory 删除食物分类
func (afc *AdminFoodController) DeleteCategory(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	err := afc.categorySvc.DeleteCategory(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "删除成功"})
}

// GetCategoryList 获取食物分类列表（管理端）
func (afc *AdminFoodController) GetCategoryList(c *gin.Context) {
	var req v1.FoodCategoryQueryReq
	if !afc.bindQueryAndValidate(c, &req) {
		return
	}

	// 设置默认分页参数
	if req.Current <= 0 {
		req.Current = 1
	}
	if req.Size <= 0 {
		req.Size = 10
	}

	res, err := afc.categorySvc.ListCategories(c.Request.Context(), &req)
	if err != nil {
		c.Error(err)
		return
	}

	// 使用分页响应格式
	response.SuccessWithPage(c, res.Records, res.Total, res.Current, res.Size)
}

// GetCategoryDetail 获取食物分类详情（管理端）
func (afc *AdminFoodController) GetCategoryDetail(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	res, err := afc.categorySvc.GetCategory(c.Request.Context(), id)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, res)
}

// UpdateCategorySortOrder 更新分类排序顺序
func (afc *AdminFoodController) UpdateCategorySortOrder(c *gin.Context) {
	id, ok := afc.parseIDParam(c, "id")
	if !ok {
		return
	}

	var req struct {
		SortOrder int `json:"sortOrder" binding:"required,min=0"`
	}
	if !afc.bindJSONAndValidate(c, &req) {
		return
	}

	err := afc.categorySvc.UpdateCategorySortOrder(c.Request.Context(), id, req.SortOrder)
	if err != nil {
		c.Error(err)
		return
	}

	response.Success(c, gin.H{"message": "更新成功"})
}

// ===== 图片上传方法 =====

// GetUploadImageUrl 生成食物图片上传URL
// 与Java版本保持一致，使用query参数：foodId和contentType
func (afc *AdminFoodController) GetUploadImageUrl(c *gin.Context) {
	// 获取query参数（与Java版本一致）
	foodIdStr := c.Query("foodId")
	contentType := c.Query("contentType")

	// 参数验证
	if foodIdStr == "" {
		response.ErrorWithMessage(c, 400, "foodId parameter is required")
		return
	}
	if contentType == "" {
		response.ErrorWithMessage(c, 400, "contentType parameter is required")
		return
	}

	// 转换foodId为int64
	foodId, err := strconv.ParseInt(foodIdStr, 10, 64)
	if err != nil || foodId <= 0 {
		response.ErrorWithMessage(c, 400, "invalid foodId parameter")
		return
	}

	// 调用食物服务生成上传URL
	uploadResult, err := afc.foodService.GenerateImageUploadURL(c.Request.Context(), foodId, contentType)
	if err != nil {
		response.ErrorWithMessage(c, 500, "failed to generate upload URL")
		return
	}

	// 直接返回预签名URL字符串（与Java版本ApiResponse<String>格式一致）
	response.Success(c, uploadResult)
}

// UpdateFoodImageUrl 更新食物图片URL
func (afc *AdminFoodController) UpdateFoodImageUrl(c *gin.Context) {
	// 获取路径参数中的食物ID
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil || id <= 0 {
		response.ErrorWithMessage(c, 400, "invalid food id")
		return
	}

	// 绑定请求体
	var req struct {
		ImageURL string `json:"imageUrl" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage(c, 400, "invalid request body")
		return
	}

	// 调用食物服务更新图片URL
	err = afc.foodService.UpdateFoodImageURL(c.Request.Context(), id, req.ImageURL)
	if err != nil {
		response.ErrorWithMessage(c, 500, "failed to update food image URL")
		return
	}

	// 返回成功结果（与Java版本ApiResponse<Boolean>格式一致）
	response.Success(c, true)
}
