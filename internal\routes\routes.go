package routes

import (
	"log"

	"github.com/gin-gonic/gin"
	"shikeyinxiang/internal/config"
	"shikeyinxiang/internal/controllers"
	"shikeyinxiang/internal/database"
	"shikeyinxiang/internal/logic"
	"shikeyinxiang/internal/middleware"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/storage"
)

// SetupRoutes 设置所有路由
func SetupRoutes(r *gin.Engine) {
	// 应用全局中间件
	r.Use(middleware.CORSMiddleware())
	r.Use(middleware.ErrorHandler())
	r.Use(middleware.LoggerMiddleware())
	// r.Use(middleware.RequestIDMiddleware())

	// TODO: 设置404和405处理器
	//r.NoRoute(middleware.NotFoundHandler())
	//r.NoMethod(middleware.MethodNotAllowedHandler())

	// 健康检查接口
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "ok",
			"message": "Service is running",
		})
	})

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由 /api/auth
		setupAuthRoutes(api)

		// 食物管理相关路由 /api/food
		setupFoodRoutes(api)

		// 饮食记录相关路由 /api/diet-records
		setupDietRecordRoutes(api)

		// TODO: 其他路由将在后续实现
		// setupUserRoutes(api)
		// setupNutritionRoutes(api)
		// setupFileRoutes(api)
		// setupAdminRoutes(api)
	}
}

// setupAuthRoutes 设置认证路由
func setupAuthRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	userRepo := repositories.NewUserRepository(database.GetDB())
	authLogic := logic.NewAuth(userRepo, &config.AppConfig.Wechat)
	authController := controllers.NewAuthController(authLogic)

	auth := api.Group("/auth")
	{
		// 公开路由（无需认证）
		auth.POST("/register", authController.Register)
		auth.POST("/user/login", authController.UserLogin)
		auth.POST("/admin/login", authController.AdminLogin)
		auth.POST("/wechat-login", authController.WechatLogin)

		// 需要认证的路由
		auth.Use(middleware.AuthMiddleware()) // 对下面的路由组统一应用认证中间件
		{
			auth.POST("/logout", authController.Logout)
			auth.POST("/change-password", authController.ChangePassword)
			auth.GET("/me", authController.GetCurrentUser)
		}
	}
}

// setupFoodRoutes 设置食物管理路由
func setupFoodRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	db := database.GetDB()
	foodRepo := repositories.NewFoodRepository(db)
	categoryRepo := repositories.NewFoodCategoryRepository(db)

	// 初始化文件服务相关依赖
	r2Storage, err := storage.NewR2Storage(&config.AppConfig.R2)
	if err != nil {
		log.Fatalf("Failed to initialize R2 storage: %v", err)
	}
	fileService := logic.NewFileLogic(r2Storage)

	// 初始化Logic层
	foodLogic := logic.NewFoodLogic(foodRepo, categoryRepo, fileService)
	categoryLogic := logic.NewFoodCategoryLogic(categoryRepo, foodRepo)

	// 初始化Controller
	foodController := controllers.NewFoodController(foodLogic, categoryLogic)
	adminFoodController := controllers.NewAdminFoodController(foodLogic, categoryLogic)

	// 用户端路由组 /api/food
	food := api.Group("/food")
	{
		// 公开路由（无需认证）
		food.GET("/list", foodController.GetFoodList)           // 获取食物列表
		food.GET("/:id", foodController.GetFoodDetail)          // 获取食物详情
		food.GET("/search", foodController.SearchFoods)         // 搜索食物
		food.GET("/categories", foodController.GetCategories)   // 获取所有分类
		food.GET("/categories/list", foodController.GetCategoryList) // 获取分类列表（分页）
		food.GET("/categories/:id", foodController.GetCategoryDetail) // 获取分类详情
		food.GET("/category/:categoryId/foods", foodController.GetFoodsByCategory) // 根据分类获取食物（修改路径避免冲突）
	}

	// 管理端路由组 /api/admin/food
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware()) // 管理端路由需要认证
	{
		adminFood := admin.Group("/food")
		{
			// 食物管理
			adminFood.POST("", adminFoodController.CreateFood)           // 创建食物
			adminFood.PUT("", adminFoodController.UpdateFood)            // 更新食物
			adminFood.DELETE("/:id", adminFoodController.DeleteFood)     // 删除食物
			adminFood.GET("/list", adminFoodController.GetFoodList)      // 获取食物列表
			adminFood.GET("/:id", adminFoodController.GetFoodDetail)     // 获取食物详情

			// 分类管理
			adminFood.POST("/categories", adminFoodController.CreateCategory)     // 创建分类
			adminFood.PUT("/categories", adminFoodController.UpdateCategory)      // 更新分类
			adminFood.DELETE("/categories/:id", adminFoodController.DeleteCategory) // 删除分类
			adminFood.GET("/categories/list", adminFoodController.GetCategoryList) // 获取分类列表
			adminFood.GET("/categories/:id", adminFoodController.GetCategoryDetail) // 获取分类详情
			adminFood.PUT("/categories/:id/sort", adminFoodController.UpdateCategorySortOrder) // 更新分类排序

			// 图片相关接口（与Java版本保持一致）
			adminFood.GET("/upload-image-url", adminFoodController.GetUploadImageUrl) // 获取图片上传URL
			adminFood.PUT("/:id/image", adminFoodController.UpdateFoodImageUrl)       // 更新食物图片URL
		}
	}
}

// setupDietRecordRoutes 设置饮食记录路由
func setupDietRecordRoutes(api *gin.RouterGroup) {
	// 初始化依赖
	db := database.GetDB()
	dietRecordRepo := repositories.NewDietRecordRepository(db)
	dietRecordFoodRepo := repositories.NewDietRecordFoodRepository(db)
	foodRepo := repositories.NewFoodRepository(db)

	// 初始化Logic层
	dietRecordLogic := logic.NewDietRecordLogic(dietRecordRepo, dietRecordFoodRepo, foodRepo)
	dietRecordAdminLogic := logic.NewDietRecordAdminLogic(dietRecordRepo, dietRecordFoodRepo, foodRepo)

	// 初始化Controller
	dietRecordController := controllers.NewDietRecordController(dietRecordLogic)
	dietRecordAdminController := controllers.NewDietRecordAdminController(dietRecordAdminLogic)

	// 用户端路由组 /api/diet-records
	dietRecords := api.Group("/diet-records")
	dietRecords.Use(middleware.AuthMiddleware()) // 用户端路由需要认证
	{
		// 饮食记录CRUD操作
		dietRecords.POST("", dietRecordController.CreateDietRecord)                    // 创建饮食记录
		dietRecords.GET("/:id", dietRecordController.GetDietRecord)                    // 获取饮食记录详情
		dietRecords.PUT("", dietRecordController.UpdateDietRecord)                     // 更新饮食记录
		dietRecords.DELETE("/:id", dietRecordController.DeleteDietRecord)              // 删除饮食记录
		dietRecords.DELETE("/batch", dietRecordController.BatchDeleteDietRecords)      // 批量删除饮食记录

		// 查询和搜索
		dietRecords.GET("/list", dietRecordController.GetDietRecordList)               // 获取饮食记录列表（分页）
		dietRecords.GET("/search", dietRecordController.SearchDietRecords)             // 搜索饮食记录
		dietRecords.GET("/date/:date", dietRecordController.GetDietRecordsByDate)      // 获取指定日期的饮食记录

		// 营养统计
		dietRecords.GET("/nutrition/stats", dietRecordController.GetNutritionStats)    // 获取营养统计
	}

	// 管理端路由组 /api/admin/diet-records
	admin := api.Group("/admin")
	admin.Use(middleware.AuthMiddleware())        // 管理端路由需要认证
	admin.Use(middleware.AdminOnlyMiddleware())   // 管理端路由需要管理员权限
	{
		adminDietRecords := admin.Group("/diet-records")
		{
			// 饮食记录管理
			adminDietRecords.GET("/:id", dietRecordAdminController.GetDietRecord)                    // 获取饮食记录详情
			adminDietRecords.GET("/list", dietRecordAdminController.GetDietRecordList)               // 获取饮食记录列表（支持跨用户）
			adminDietRecords.DELETE("/:id", dietRecordAdminController.DeleteDietRecord)              // 删除饮食记录
			adminDietRecords.DELETE("/batch", dietRecordAdminController.BatchDeleteDietRecords)      // 批量删除饮食记录

			// 营养统计和分析
			adminDietRecords.GET("/nutrition/stats", dietRecordAdminController.GetNutritionStats)    // 获取营养统计（跨用户）
			adminDietRecords.GET("/users/:userId/nutrition/stats", dietRecordAdminController.GetUserNutritionStats) // 获取指定用户营养统计
			adminDietRecords.GET("/system/stats", dietRecordAdminController.GetSystemStats)          // 获取系统级统计

			// 数据导出
			adminDietRecords.GET("/export", dietRecordAdminController.ExportDietRecords)             // 导出饮食记录数据
		}
	}
}
