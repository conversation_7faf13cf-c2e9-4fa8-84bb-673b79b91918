package logic

import (
	"context"
	"errors"
	"fmt"
	"io"

	v1 "shikeyinxiang/api/v1"
	"shikeyinxiang/internal/entities"
	"shikeyinxiang/internal/repositories"
	"shikeyinxiang/internal/service"
)

// foodLogic 食物业务逻辑实现
type foodLogic struct {
	foodRepo     repositories.IFoodRepo
	categoryRepo repositories.IFoodCategoryRepo
	fileService  service.IFileService
}

// NewFoodLogic 创建食物业务逻辑实例
func NewFoodLogic(foodRepo repositories.IFoodRepo, categoryRepo repositories.IFoodCategoryRepo, fileService service.IFileService) service.IFoodService {
	return &foodLogic{
		foodRepo:     foodRepo,
		categoryRepo: categoryRepo,
		fileService:  fileService,
	}
}

// 确保 foodLogic 实现了 IFoodService 接口
var _ service.IFoodService = &foodLogic{}

// CreateFood 创建食物
func (f *foodLogic) CreateFood(ctx context.Context, req *v1.FoodCreateReq) (*v1.FoodResponse, error) {
	// 参数验证
	if req.Name == "" {
		return nil, &ParameterError{Field: "name", Message: "is required"}
	}

	// 验证营养成分数据的合理性
	if err := f.validateNutritionData(req.Grams, req.Calories, req.Protein, req.Fat, req.SatFat, req.Carbs); err != nil {
		return nil, err
	}

	// 验证分类ID有效性
	if req.CategoryID != nil {
		if _, err := f.categoryRepo.GetByID(*req.CategoryID); err != nil {
			var categoryNotFound *repositories.FoodCategoryNotFoundError
			if errors.As(err, &categoryNotFound) {
				return nil, &FoodCategoryNotFoundError{CategoryID: *req.CategoryID}
			}
			return nil, fmt.Errorf("logic: failed to validate category: %w", err)
		}
	}

	// 创建食物实体
	food := entities.NewFood(req.Name, "")
	if req.Measure != nil {
		food.Measure = req.Measure
	}
	food.SetNutritionInfo(req.Grams, req.Calories, req.Protein, req.Fat, req.SatFat, req.Carbs, req.Fiber)
	if req.ImageURL != nil {
		food.SetImageURL(*req.ImageURL)
	}
	if req.CategoryID != nil {
		food.SetCategory(*req.CategoryID)
	}

	// 保存食物
	if err := f.foodRepo.Create(food); err != nil {
		return nil, fmt.Errorf("logic: failed to create food: %w", err)
	}

	// 获取完整的食物信息（包含分类）
	createdFood, err := f.foodRepo.GetByIDWithCategory(food.ID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get created food: %w", err)
	}

	return f.convertToFoodResponse(createdFood), nil
}

// GetFood 获取食物详情
func (f *foodLogic) GetFood(ctx context.Context, id int) (*v1.FoodResponse, error) {
	if id <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}

	food, err := f.foodRepo.GetByIDWithCategory(id)
	if err != nil {
		var foodNotFound *repositories.FoodNotFoundError
		if errors.As(err, &foodNotFound) {
			return nil, &FoodNotFoundError{FoodID: id}
		}
		return nil, fmt.Errorf("logic: failed to get food: %w", err)
	}

	return f.convertToFoodResponse(food), nil
}

// UpdateFood 更新食物
func (f *foodLogic) UpdateFood(ctx context.Context, req *v1.FoodUpdateReq) (*v1.FoodResponse, error) {
	// 参数验证
	if req.ID <= 0 {
		return nil, &ParameterError{Field: "id", Message: "must be positive"}
	}
	if req.Name == "" {
		return nil, &ParameterError{Field: "name", Message: "is required"}
	}

	// 验证营养成分数据的合理性
	if err := f.validateNutritionData(req.Grams, req.Calories, req.Protein, req.Fat, req.SatFat, req.Carbs); err != nil {
		return nil, err
	}

	// 验证食物是否存在，并获取旧的图片路径
	food, err := f.foodRepo.GetByID(req.ID)
	if err != nil {
		var foodNotFound *repositories.FoodNotFoundError
		if errors.As(err, &foodNotFound) {
			return nil, &FoodNotFoundError{FoodID: req.ID}
		}
		return nil, fmt.Errorf("logic: failed to get food for update: %w", err)
	}

	// 获取旧的图片路径
	var oldImagePath string
	if food.ImageURL != nil {
		oldImagePath = *food.ImageURL
	}

	// 验证分类ID有效性
	if req.CategoryID != nil {
		if _, err := f.categoryRepo.GetByID(*req.CategoryID); err != nil {
			var categoryNotFound *repositories.FoodCategoryNotFoundError
			if errors.As(err, &categoryNotFound) {
				return nil, &FoodCategoryNotFoundError{CategoryID: *req.CategoryID}
			}
			return nil, fmt.Errorf("logic: failed to validate category: %w", err)
		}
	}

	// 更新食物信息
	food.FoodName = &req.Name
	food.Measure = req.Measure
	food.SetNutritionInfo(req.Grams, req.Calories, req.Protein, req.Fat, req.SatFat, req.Carbs, req.Fiber)
	if req.ImageURL != nil {
		food.SetImageURL(*req.ImageURL)
	}
	if req.CategoryID != nil {
		food.SetCategory(*req.CategoryID)
	}

	// 保存更新
	if err := f.foodRepo.Update(food); err != nil {
		return nil, fmt.Errorf("logic: failed to update food: %w", err)
	}

	// 检查图片是否变化，如果变化则异步删除旧图片（与Java版本一致）
	var newImagePath string
	if food.ImageURL != nil {
		newImagePath = *food.ImageURL
	}
	if oldImagePath != "" && oldImagePath != newImagePath {
		// 异步删除旧图片
		go func() {
			if err := f.fileService.DeleteFile(context.Background(), oldImagePath); err != nil {
				// 删除失败只记录错误，不影响主流程（与Java版本行为一致）
				// TODO: 添加日志记录
			}
		}()
	}

	// 获取更新后的完整信息
	updatedFood, err := f.foodRepo.GetByIDWithCategory(food.ID)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to get updated food: %w", err)
	}

	return f.convertToFoodResponse(updatedFood), nil
}

// DeleteFood 删除食物
func (f *foodLogic) DeleteFood(ctx context.Context, id int) error {
	if id <= 0 {
		return &ParameterError{Field: "id", Message: "must be positive"}
	}

	// 先获取食物信息，以便删除图片
	food, err := f.foodRepo.GetByID(id)
	if err != nil {
		var foodNotFound *repositories.FoodNotFoundError
		if errors.As(err, &foodNotFound) {
			return &FoodNotFoundError{FoodID: id}
		}
		return fmt.Errorf("logic: failed to get food for delete: %w", err)
	}

	// 获取图片路径
	var imagePath string
	if food.ImageURL != nil {
		imagePath = *food.ImageURL
	}

	// 删除食物记录
	if err := f.foodRepo.Delete(id); err != nil {
		return fmt.Errorf("logic: failed to delete food: %w", err)
	}

	// 如果存在图片，则异步删除（与Java版本一致）
	if imagePath != "" {
		go func() {
			if err := f.fileService.DeleteFile(context.Background(), imagePath); err != nil {
				// 删除失败只记录错误，不影响主流程（与Java版本行为一致）
				// TODO: 添加日志记录
			}
		}()
	}

	return nil
}

// ListFoods 获取食物列表
func (f *foodLogic) ListFoods(ctx context.Context, req *v1.FoodQueryReq) (*v1.FoodListResponse, error) {
	// 设置默认分页参数
	page := req.Current
	if page <= 0 {
		page = 1
	}
	size := req.Size
	if size <= 0 {
		size = 10
	}

	offset := (page - 1) * size

	var foods []*entities.Food
	var total int64
	var err error

	// 根据查询条件选择不同的查询方法
	if req.Keyword != "" || req.CategoryID != nil {
		foods, total, err = f.foodRepo.SearchWithCategory(req.Keyword, req.CategoryID, offset, size)
	} else {
		foods, total, err = f.foodRepo.ListWithCategory(offset, size)
	}

	if err != nil {
		return nil, fmt.Errorf("logic: failed to list foods: %w", err)
	}

	// 转换为响应格式
	records := make([]*v1.FoodResponse, len(foods))
	for i, food := range foods {
		records[i] = f.convertToFoodResponse(food)
	}

	return &v1.FoodListResponse{
		Total:   total,
		Records: records,
		Current: page,
		Size:    size,
	}, nil
}

// SearchFoods 搜索食物
func (f *foodLogic) SearchFoods(ctx context.Context, keyword string, categoryID *int, page, size int) (*v1.FoodListResponse, error) {
	// 设置默认分页参数
	if page <= 0 {
		page = 1
	}
	if size <= 0 {
		size = 10
	}

	offset := (page - 1) * size

	foods, total, err := f.foodRepo.SearchWithCategory(keyword, categoryID, offset, size)
	if err != nil {
		return nil, fmt.Errorf("logic: failed to search foods: %w", err)
	}

	// 转换为响应格式
	records := make([]*v1.FoodResponse, len(foods))
	for i, food := range foods {
		records[i] = f.convertToFoodResponse(food)
	}

	return &v1.FoodListResponse{
		Total:   total,
		Records: records,
		Current: page,
		Size:    size,
	}, nil
}

// validateNutritionData 验证营养成分数据的合理性
func (f *foodLogic) validateNutritionData(grams, calories, protein, fat, satFat, carbs *float64) error {
	// 检查数值是否为负数
	if grams != nil && *grams < 0 {
		return &ParameterError{Field: "grams", Message: "cannot be negative"}
	}
	if calories != nil && *calories < 0 {
		return &ParameterError{Field: "calories", Message: "cannot be negative"}
	}
	if protein != nil && *protein < 0 {
		return &ParameterError{Field: "protein", Message: "cannot be negative"}
	}
	if fat != nil && *fat < 0 {
		return &ParameterError{Field: "fat", Message: "cannot be negative"}
	}
	if satFat != nil && *satFat < 0 {
		return &ParameterError{Field: "satFat", Message: "cannot be negative"}
	}
	if carbs != nil && *carbs < 0 {
		return &ParameterError{Field: "carbs", Message: "cannot be negative"}
	}

	// 检查饱和脂肪不能超过总脂肪
	if satFat != nil && fat != nil && *satFat > *fat {
		return &ParameterError{Field: "satFat", Message: "cannot exceed total fat"}
	}

	return nil
}

// convertToFoodResponse 转换实体为响应格式
func (f *foodLogic) convertToFoodResponse(food *entities.Food) *v1.FoodResponse {
	response := &v1.FoodResponse{
		ID:         food.ID,
		Name:       food.FoodName,        // 使用name字段与Java保持一致
		Measure:    food.Measure,
		Grams:      food.Grams,
		Calories:   food.Calories,
		Protein:    food.Protein,
		Fat:        food.Fat,
		SatFat:     food.SatFat,
		Carbs:      food.Carbs,
		CategoryID: food.CategoryID,
		Desc:       nil,                 // Java项目中这些字段存在但为空
		Unit:       nil,                 // Java项目中这些字段存在但为空
		ImageURL:   nil,                 // 将在下面设置
		Remark:     nil,                 // Java项目中这些字段存在但为空
	}

	// 设置图片URL，如果有原始URL，则生成预签名下载URL（有效期60分钟，与Java版本一致）
	if food.ImageURL != nil && *food.ImageURL != "" {
		downloadURL, err := f.fileService.GenerateDownloadPresignedURL(context.Background(), *food.ImageURL, 60)
		if err != nil {
			// 如果生成失败，仍然设置原始URL（与Java版本行为一致）
			response.ImageURL = food.ImageURL
		} else {
			response.ImageURL = &downloadURL
		}
	} else {
		response.ImageURL = food.ImageURL // 可能为nil或空字符串
	}

	// 添加分类名称（与Java保持一致，返回字符串而不是对象）
	if food.Category != nil {
		response.Category = &food.Category.Name
	}

	return response
}

// GenerateImageUploadURL 生成食物图片上传URL
func (f *foodLogic) GenerateImageUploadURL(ctx context.Context, foodID int64, contentType string) (string, error) {
	// 参数验证
	if foodID <= 0 {
		return "", &ParameterError{Field: "foodId", Message: "must be positive"}
	}
	if contentType == "" {
		return "", &ParameterError{Field: "contentType", Message: "cannot be empty"}
	}

	// 调用文件服务生成上传URL（30分钟有效期，与Java版本一致）
	uploadResult, err := f.fileService.GenerateUploadPresignedURL(ctx, foodID, "foodimage", contentType, 30)
	if err != nil {
		return "", fmt.Errorf("logic: failed to generate upload URL: %w", err)
	}

	return uploadResult, nil
}

// GetFoodImageURL 获取食物图片下载URL
func (f *foodLogic) GetFoodImageURL(ctx context.Context, foodID int) (string, error) {
	// 参数验证
	if foodID <= 0 {
		return "", &ParameterError{Field: "foodId", Message: "must be positive"}
	}

	// 获取食物信息
	food, err := f.foodRepo.GetByID(ctx, foodID)
	if err != nil {
		return "", fmt.Errorf("logic: failed to get food: %w", err)
	}
	if food == nil {
		return "", &NotFoundError{Resource: "food", ID: fmt.Sprintf("%d", foodID)}
	}

	// 如果食物没有图片，返回空字符串
	if food.ImageURL == nil || *food.ImageURL == "" {
		return "", nil
	}

	// 生成预签名下载URL（有效期60分钟，与Java版本一致）
	downloadURL, err := f.fileService.GenerateDownloadPresignedURL(ctx, *food.ImageURL, 60)
	if err != nil {
		// 如果生成失败，返回原始URL（与Java版本行为一致）
		return *food.ImageURL, nil
	}

	return downloadURL, nil
}

// UpdateFoodImageURL 更新食物图片URL
func (f *foodLogic) UpdateFoodImageURL(ctx context.Context, foodID int, imageURL string) error {
	// 参数验证
	if foodID <= 0 {
		return &ParameterError{Field: "foodId", Message: "must be positive"}
	}

	// 获取旧的食物信息，以便检查图片是否变化
	oldFood, err := f.foodRepo.GetByID(foodID)
	if err != nil {
		var foodNotFound *repositories.FoodNotFoundError
		if errors.As(err, &foodNotFound) {
			return &FoodNotFoundError{FoodID: foodID}
		}
		return fmt.Errorf("logic: failed to get food: %w", err)
	}

	// 获取旧的图片路径
	var oldImagePath string
	if oldFood.ImageURL != nil {
		oldImagePath = *oldFood.ImageURL
	}

	// 更新食物图片URL
	err = f.foodRepo.UpdateImageURL(foodID, imageURL)
	if err != nil {
		return fmt.Errorf("logic: failed to update food image URL: %w", err)
	}

	// 检查图片是否变化，如果变化则异步删除旧图片（与Java版本一致）
	if oldImagePath != "" && oldImagePath != imageURL {
		// 异步删除旧图片
		go func() {
			if err := f.fileService.DeleteFile(context.Background(), oldImagePath); err != nil {
				// 删除失败只记录错误，不影响主流程（与Java版本行为一致）
				// TODO: 添加日志记录
			}
		}()
	}

	return nil
}

// UploadImageToR2 代理上传图片到R2（解决CORS问题）
func (f *foodLogic) UploadImageToR2(ctx context.Context, foodID int64, fileName string, file io.Reader, contentType string) error {
	// 参数验证
	if foodID <= 0 {
		return &ParameterError{Field: "foodId", Message: "must be positive"}
	}
	if fileName == "" {
		return &ParameterError{Field: "fileName", Message: "cannot be empty"}
	}
	if file == nil {
		return &ParameterError{Field: "file", Message: "cannot be nil"}
	}
	if contentType == "" {
		return &ParameterError{Field: "contentType", Message: "cannot be empty"}
	}

	// 调用文件服务直接上传文件到R2
	err := f.fileService.UploadFileToR2(ctx, fileName, file, contentType)
	if err != nil {
		return fmt.Errorf("logic: failed to upload file to R2: %w", err)
	}

	return nil
}
